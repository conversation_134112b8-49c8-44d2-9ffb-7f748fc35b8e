PODS:
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - Flutter (1.0.0)
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - ReachabilitySwift (5.0.0)
  - shared_preferences_ios (0.0.1):
    - Flutter
  - Toast (4.0.0)

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - Flutter (from `Flutter`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - shared_preferences_ios (from `.symlinks/plugins/shared_preferences_ios/ios`)

SPEC REPOS:
  trunk:
    - ReachabilitySwift
    - Toast

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  Flutter:
    :path: Flutter
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  shared_preferences_ios:
    :path: ".symlinks/plugins/shared_preferences_ios/ios"

SPEC CHECKSUMS:
  connectivity_plus: 413a8857dd5d9f1c399a39130850d02fe0feaf7e
  Flutter: 50d75fe2f02b26cc09d224853bb45737f8b3214a
  fluttertoast: 16fbe6039d06a763f3533670197d01fc73459037
  ReachabilitySwift: 985039c6f7b23a1da463388634119492ff86c825
  shared_preferences_ios: 548a61f8053b9b8a49ac19c1ffbc8b92c50d68ad
  Toast: 91b396c56ee72a5790816f40d3a94dd357abc196

PODFILE CHECKSUM: aafe91acc616949ddb318b77800a7f51bffa2a4c

COCOAPODS: 1.11.3
