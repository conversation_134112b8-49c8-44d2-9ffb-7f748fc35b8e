import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:smartfarming_bapeltan/common/url.dart';

class Kontrol {
  static Future<dynamic> updateKontrolByStatus(
      int status, int idUser, int idAlat, int idKontrol) async {
    String apiURL = UrlData().url_update_kontrol + '$idUser/$idAlat/$idKontrol';

    Map<String, String> header = {
      'Content-type': 'application/json',
    };

    var body = jsonEncode({'isON': status});

    var apiResult = await http.patch(
      Uri.parse(apiURL),
      body: body,
      headers: header,
    );

    var jsonObject = json.decode(apiResult.body);

    return jsonObject;
  }

  static Future<dynamic> updateKontrolByAuto(
      int status, int idUser, int idAlat, int idKontrol) async {
    String apiURL = UrlData().url_update_kontrol + '$idUser/$idAlat/$idKontrol';
    // "http://bppltest.co.vu/dummyBackend/kontrol/$idUser/$idAlat/$idKontrol";

    Map<String, String> header = {
      'Content-type': 'application/json',
    };

    var body = jsonEncode({'automated': status});

    var apiResult = await http.patch(
      Uri.parse(apiURL),
      body: body,
      headers: header,
    );

    var jsonObject = json.decode(apiResult.body);

    return jsonObject;
  }

  static Future<dynamic> updateKontrolByAutoParameter(
      String status, int idUser, int idAlat, int idKontrol) async {
    String apiURL = UrlData().url_update_kontrol + '$idUser/$idAlat/$idKontrol';

    Map<String, String> header = {
      'Content-type': 'application/json',
    };

    var body = jsonEncode({'parameter': status});

    var apiResult = await http.patch(
      Uri.parse(apiURL),
      body: body,
      headers: header,
    );

    var jsonObject = json.decode(apiResult.body);

    return jsonObject;
  }
}
