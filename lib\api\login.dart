import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:smartfarming_bapeltan/common/url.dart';

class Login {
  static Future<dynamic> loginWithUsernameAndPassword(
      String username, String password) async {
    String apiURL = UrlData().url_login;

    Map<String, String> header = {
      'Content-type': 'application/json',
    };

    var body = jsonEncode({'username': username, 'password': password});

    var apiResult = await http.post(
      Uri.parse(apiURL),
      body: body,
      headers: header,
    );

    var jsonObject = json.decode(apiResult.body);

    return jsonObject;
  }
}
