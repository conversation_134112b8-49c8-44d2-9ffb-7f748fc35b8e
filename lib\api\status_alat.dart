import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:smartfarming_bapeltan/common/url.dart';
import 'package:smartfarming_bapeltan/model/status_alat_model.dart';

class StatusAlat {
  static StatusAlatModel? model;

  static getStatusAlatByIdUserAndIdAlat(int idUser, int idAlat) async {
    try {
      String apiURL = UrlData().url_status_alat + '$idUser/$idAlat';

      var apiResult = await http.get(Uri.parse(apiURL));

      if (apiResult.statusCode == 200) {
        /// successfully get data
        var jsonObject = json.decode(apiResult.body);
        model = StatusAlatModel.fromJson(jsonObject);
      } else {
        /// failure get data
        print(apiResult.statusCode);
      }
    } catch (error) {
      print(error);
    }
  }
}
