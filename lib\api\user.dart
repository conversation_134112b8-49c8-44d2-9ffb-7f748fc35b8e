import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:smartfarming_bapeltan/common/url.dart';
import 'package:smartfarming_bapeltan/model/user_model.dart';

class User {
  static UserModel? userModel;

  static getDataUser(int userId) async {
    try {
      String apiURL = UrlData().url_summary + '$userId';

      var apiResult = await http.get(Uri.parse(apiURL));

      if (apiResult.statusCode == 200) {
        /// successfully get data
        var jsonObject = json.decode(apiResult.body);
        userModel = UserModel.fromJson(jsonObject);
      } else {
        /// failure get data
        print(apiResult.statusCode);
      }
    } catch (error) {
      print(error);
    }
  }
}
